<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coin Flipper Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { background: white; padding: 25px; border-radius: 10px; max-width: 800px; margin: 0 auto; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        h1 { text-align: center; color: #2c3e50; margin-bottom: 10px; }
        .subtitle { text-align: center; color: #7f8c8d; margin-bottom: 30px; font-size: 16px; }
        .coin-container { display: flex; justify-content: center; margin: 25px 0; padding: 25px; background: linear-gradient(135deg, #2c3e50, #34495e); border-radius: 10px; box-shadow: inset 0 2px 10px rgba(0,0,0,0.3); }
        .coin-canvas { border: 2px solid #ecf0f1; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .controls { text-align: center; margin: 25px 0; }
        .btn { padding: 15px 30px; margin: 10px; border: none; border-radius: 8px; background: linear-gradient(135deg, #3498db, #2980b9); color: white; cursor: pointer; font-size: 16px; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 6px 12px rgba(0,0,0,0.3); }
        .btn:disabled { background: #bdc3c7; cursor: not-allowed; transform: none; box-shadow: none; }
        .btn.heads { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .btn.tails { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
        .btn.idle { background: linear-gradient(135deg, #95a5a6, #7f8c8d); }
        .result { background: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; font-size: 20px; font-weight: bold; border: 2px solid #bdc3c7; }
        .result.success { background: linear-gradient(135deg, #d5f4e6, #c8e6c9); color: #27ae60; border-color: #27ae60; }
        .result.error { background: linear-gradient(135deg, #fadbd8, #f5b7b1); color: #e74c3c; border-color: #e74c3c; }
        .debug { background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap; max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border: 1px solid #dee2e6; }
        .stat-value { font-size: 24px; font-weight: bold; color: #2c3e50; }
        .stat-label { font-size: 14px; color: #7f8c8d; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🪙 Coin Flipper Test</h1>
        <p class="subtitle">ทดสอบการควบคุมสถานะเหรียญด้วยปุ่มโต้ตอบ</p>
        
        <div class="coin-container">
            <canvas id="coinCanvas" width="400" height="400" class="coin-canvas"></canvas>
        </div>

        <div class="controls">
            <button id="idleBtn" class="btn idle">Idle</button>
            <button id="headsBtn" class="btn heads">👑 หัว</button>
            <button id="tailsBtn" class="btn tails">🦅 ก้อย</button>
        </div>

        <div class="controls">
            <h3 style="text-align: center; color: #2c3e50; margin: 20px 0 10px 0;">Win/Lose Scene Testing</h3>
            <button id="winHeadsBtn" class="btn" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">🏆 Win (หัว)</button>
            <button id="winTailsBtn" class="btn" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">🏆 Win (ก้อย)</button>
            <button id="loseHeadsBtn" class="btn" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">❌ Lose (หัว)</button>
            <button id="loseTailsBtn" class="btn" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">❌ Lose (ก้อย)</button>
        </div>

        <div class="result" id="result">กำลังโหลด...</div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="successCount">0</div>
                <div class="stat-label">ครั้งที่สำเร็จ</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalCount">0</div>
                <div class="stat-label">ทดสอบทั้งหมด</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="successRate">0%</div>
                <div class="stat-label">อัตราความสำเร็จ</div>
            </div>
        </div>

        <div class="debug" id="debug">กำลังโหลด...</div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="../src/coin-flipper.js"></script>
    
    <script>
        let coinFlipper = null;
        let debugElement = document.getElementById('debug');
        let resultElement = document.getElementById('result');
        let successCount = 0;
        let totalCount = 0;

        function log(message) {
            console.log(message);
            debugElement.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            debugElement.scrollTop = debugElement.scrollHeight;
        }

        function updateResult(message, isSuccess = null) {
            resultElement.textContent = message;
            resultElement.className = 'result';
            if (isSuccess === true) {
                resultElement.className += ' success';
            } else if (isSuccess === false) {
                resultElement.className += ' error';
            }
        }

        function updateStats() {
            document.getElementById('successCount').textContent = successCount;
            document.getElementById('totalCount').textContent = totalCount;
            const rate = totalCount > 0 ? Math.round((successCount / totalCount) * 100) : 0;
            document.getElementById('successRate').textContent = rate + '%';
        }

        function checkFlatness() {
            if (!coinFlipper || !coinFlipper.coinRenderer) {
                return { isFlat: false, isOnGround: false, message: 'CoinFlipper not ready' };
            }

            const coin = coinFlipper.coinRenderer.coin;
            const rotX = coin.rotation.x * 180 / Math.PI;
            const rotY = coin.rotation.y * 180 / Math.PI;
            const rotZ = coin.rotation.z * 180 / Math.PI;
            const posY = coin.position.y;
            const groundY = coinFlipper.coinRenderer.groundY;
            const coinHalfHeight = coinFlipper.coinRenderer.coinHalfHeight;
            const expectedY = groundY + coinHalfHeight;

            // ตรวจสอบว่าเหรียญนอนแบน (rotation X ต้องเป็น 0° หรือ 180° พอดี)
            const isFlat = Math.abs(rotX) < 0.5 || Math.abs(rotX - 180) < 0.5;
            
            // ตรวจสอบว่าเหรียญอยู่บนพื้น
            const isOnGround = Math.abs(posY - expectedY) < 0.01;
            return { isFlat, isOnGround, rotX, rotY, rotZ, posY, expectedY };
        }

        async function init() {
            try {
                log('เริ่มต้น CoinFlipper...');
                updateResult('กำลังโหลด...');
                
                coinFlipper = new CoinFlipper('coinCanvas', {
                    idleSpeed: 0.03,
                    flipDuration: 2000,
                    enableSound: true
                });

                await coinFlipper.ready();
                log('CoinFlipper พร้อมใช้งาน!');

                await coinFlipper.startIdle();
                log('เริ่ม idle animation');
                updateResult('พร้อมทดสอบ - เหรียญอยู่ในสถานะ Idle');

                setupEventListeners();
                updateButtonStates();

            } catch (error) {
                log('ข้อผิดพลาด: ' + error.message);
                updateResult('ข้อผิดพลาด: ' + error.message, false);
            }
        }

        function setupEventListeners() {
            document.getElementById('idleBtn').onclick = () => resetToIdle();
            document.getElementById('headsBtn').onclick = () => testFlip('heads');
            document.getElementById('tailsBtn').onclick = () => testFlip('tails');

            // Win/Lose scene buttons
            document.getElementById('winHeadsBtn').onclick = () => testWinLoseFlip('heads', 'win');
            document.getElementById('winTailsBtn').onclick = () => testWinLoseFlip('tails', 'win');
            document.getElementById('loseHeadsBtn').onclick = () => testWinLoseFlip('heads', 'lose');
            document.getElementById('loseTailsBtn').onclick = () => testWinLoseFlip('tails', 'lose');
        }

        function updateButtonStates() {
            const idleBtn = document.getElementById('idleBtn');
            const headsBtn = document.getElementById('headsBtn');
            const tailsBtn = document.getElementById('tailsBtn');

            // Win/Lose buttons
            const winHeadsBtn = document.getElementById('winHeadsBtn');
            const winTailsBtn = document.getElementById('winTailsBtn');
            const loseHeadsBtn = document.getElementById('loseHeadsBtn');
            const loseTailsBtn = document.getElementById('loseTailsBtn');

            if (!coinFlipper) {
                // If coin flipper not ready, disable all buttons
                idleBtn.disabled = true;
                headsBtn.disabled = true;
                tailsBtn.disabled = true;
                winHeadsBtn.disabled = true;
                winTailsBtn.disabled = true;
                loseHeadsBtn.disabled = true;
                loseTailsBtn.disabled = true;
                return;
            }

            const status = coinFlipper.status;

            if (status.isFlipping) {
                // While flipping, disable all buttons
                idleBtn.disabled = true;
                headsBtn.disabled = true;
                tailsBtn.disabled = true;
                winHeadsBtn.disabled = true;
                winTailsBtn.disabled = true;
                loseHeadsBtn.disabled = true;
                loseTailsBtn.disabled = true;
            } else if (status.isIdle) {
                // In idle state, enable all flip buttons, keep idle button enabled
                idleBtn.disabled = false;
                headsBtn.disabled = false;
                tailsBtn.disabled = false;
                winHeadsBtn.disabled = false;
                winTailsBtn.disabled = false;
                loseHeadsBtn.disabled = false;
                loseTailsBtn.disabled = false;
            } else {
                // Not idle and not flipping (result state), enable idle button, disable flip buttons
                idleBtn.disabled = false;
                headsBtn.disabled = true;
                tailsBtn.disabled = true;
                winHeadsBtn.disabled = true;
                winTailsBtn.disabled = true;
                loseHeadsBtn.disabled = true;
                loseTailsBtn.disabled = true;
            }
        }

        async function resetToIdle() {
            try {
                log('รีเซ็ตกลับสู่สถานะ Idle...');
                updateResult('กำลังรีเซ็ต...');

                await coinFlipper.startIdle();
                log('รีเซ็ตเสร็จสิ้น - กลับสู่สถานะ Idle');
                updateResult('พร้อมทดสอบ - เหรียญอยู่ในสถานะ Idle');
                updateButtonStates();

            } catch (error) {
                log('ข้อผิดพลาดในการรีเซ็ต: ' + error.message);
                updateResult('ข้อผิดพลาดในการรีเซ็ต: ' + error.message, false);
            }
        }

        async function testFlip(result = null) {
            try {
                // Check if coin flipper is in idle state before allowing flip
                if (!coinFlipper.status.isIdle) {
                    log('ไม่สามารถทอยได้ - เหรียญไม่อยู่ในสถานะ Idle');
                    updateResult('ไม่สามารถทอยได้ - กรุณากดปุ่ม Idle ก่อน', false);
                    return;
                }

                updateButtonStates(); // This will disable all buttons during flip

                totalCount++;
                const testType = result === 'heads' ? 'หัว' : result === 'tails' ? 'ก้อย' : 'สุ่ม';
                log(`\n=== ทดสอบครั้งที่ ${totalCount}: ${testType} ===`);
                updateResult(`กำลังทดสอบ ${testType}... (ครั้งที่ ${totalCount})`);

                await coinFlipper.stopIdle();
                log('หยุด idle animation');

                const flipResult = await coinFlipper.toss(result);
                log(`ผลลัพธ์การทอย: ${flipResult}`);

                // ตรวจสอบทันทีเพราะใช้ forceFlat()
                setTimeout(async () => {
                    const check = checkFlatness();

                    if (check.isFlat && check.isOnGround) {
                        successCount++;
                        log('✅ สำเร็จ! เหรียญถูกบังคับให้นอนราบบนพื้นแล้ว');
                        updateResult(`✅ สำเร็จ! ผลลัพธ์: ${flipResult} - เหรียญนอนราบบนพื้น (${successCount}/${totalCount})`, true);
                        // เล่นเสียงชนะ
                        try {
                            await coinFlipper.playWinSound();
                        } catch (error) {
                            log('ไม่สามารถเล่นเสียงชนะได้: ' + error.message);
                        }
                    } else {
                        log('❌ ล้มเหลว! เหรียญยังไม่นอนราบหรือไม่อยู่บนพื้น');
                        let issues = [];
                        if (!check.isFlat) issues.push('ไม่ราบ');
                        if (!check.isOnGround) issues.push('ไม่อยู่บนพื้น');
                        updateResult(`❌ ล้มเหลว! ผลลัพธ์: ${flipResult} - ${issues.join(', ')} (${successCount}/${totalCount})`, false);
                        // เล่นเสียงแพ้
                        try {
                            await coinFlipper.playLoseSound();
                        } catch (error) {
                            log('ไม่สามารถเล่นเสียงแพ้ได้: ' + error.message);
                        }
                    }

                    updateStats();

                    // DO NOT automatically return to idle - user must manually click Idle button
                    // Update button states to reflect the new state (idle disabled, heads/tails disabled)
                    updateButtonStates();
                    log('การทดสอบเสร็จสิ้น - กดปุ่ม Idle เพื่อรีเซ็ต');

                }, 300); // รอเพียง 300ms เพราะใช้ forceFlat()

            } catch (error) {
                log('ข้อผิดพลาดในการทดสอบ: ' + error.message);
                updateResult('ข้อผิดพลาด: ' + error.message, false);
                updateButtonStates();
            }
        }

        async function testWinLoseFlip(result, winLoseState) {
            try {
                // Check if coin flipper is in idle state before allowing flip
                if (!coinFlipper.status.isIdle) {
                    log('ไม่สามารถทอยได้ - เหรียญไม่อยู่ในสถานะ Idle');
                    updateResult('ไม่สามารถทอยได้ - กรุณากดปุ่ม Idle ก่อน', false);
                    return;
                }

                updateButtonStates(); // This will disable all buttons during flip

                totalCount++;
                const testType = `${winLoseState === 'win' ? '🏆 Win' : '❌ Lose'} (${result === 'heads' ? 'หัว' : 'ก้อย'})`;
                log(`\n=== ทดสอบ Win/Lose ครั้งที่ ${totalCount}: ${testType} ===`);
                updateResult(`กำลังทดสอบ ${testType}... (ครั้งที่ ${totalCount})`);

                await coinFlipper.stopIdle();
                log('หยุด idle animation');

                // ใช้ API ใหม่: toss(result, winLoseState)
                const flipResult = await coinFlipper.toss(result, winLoseState);
                log(`ผลลัพธ์การทอย: ${flipResult}, Win/Lose State: ${winLoseState}`);

                // ตรวจสอบทันทีเพราะใช้ forceFlat()
                setTimeout(async () => {
                    const check = checkFlatness();

                    if (check.isFlat && check.isOnGround) {
                        successCount++;
                        log(`✅ สำเร็จ! เหรียญถูกบังคับให้นอนราบบนพื้นแล้ว พร้อม ${winLoseState} scene`);
                        updateResult(`✅ สำเร็จ! ผลลัพธ์: ${flipResult} - ${testType} scene แสดงแล้ว (${successCount}/${totalCount})`, true);

                        // เล่นเสียงตาม win/lose state
                        try {
                            if (winLoseState === 'win') {
                                await coinFlipper.playWinSound();
                            } else {
                                await coinFlipper.playLoseSound();
                            }
                        } catch (error) {
                            log('ไม่สามารถเล่นเสียงได้: ' + error.message);
                        }
                    } else {
                        log('❌ ล้มเหลว! เหรียญยังไม่นอนราบหรือไม่อยู่บนพื้น');
                        let issues = [];
                        if (!check.isFlat) issues.push('ไม่ราบ');
                        if (!check.isOnGround) issues.push('ไม่อยู่บนพื้น');
                        updateResult(`❌ ล้มเหลว! ผลลัพธ์: ${flipResult} - ${issues.join(', ')} (${successCount}/${totalCount})`, false);
                    }

                    updateStats();

                    // DO NOT automatically return to idle - user must manually click Idle button
                    updateButtonStates();
                    log('การทดสอบ Win/Lose เสร็จสิ้น - กดปุ่ม Idle เพื่อรีเซ็ต');

                }, 300); // รอเพียง 300ms เพราะใช้ forceFlat()

            } catch (error) {
                log('ข้อผิดพลาดในการทดสอบ Win/Lose: ' + error.message);
                updateResult('ข้อผิดพลาด: ' + error.message, false);
                updateButtonStates();
            }
        }

        window.addEventListener('load', init);
    </script>
</body>
</html>
